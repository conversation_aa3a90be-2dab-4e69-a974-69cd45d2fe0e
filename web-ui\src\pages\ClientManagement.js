import { useState, useEffect , useCallback} from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import ClientTasks from '../components/ClientDetail/ClientTasks';
import ClientHistory from '../components/ClientDetail/ClientHistory';

const ClientManagement = () => {
  const [clients, setClients] = useState([]);
  const [selectedClient, setSelectedClient] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');
  const { clientId } = useParams();
  const navigate = useNavigate();
  const { token } = useAuth();
  const { socket, isConnected } = useSocket();

  useEffect(() => {
    loadClients();
    // Refresh clients every 30 seconds
    const interval = setInterval(loadClients, 30000);
    return () => clearInterval(interval);
  }, [token, loadClients]);

  // Handle URL-based client selection and real-time updates
  useEffect(() => {
    if (clientId && clients.length > 0) {
      const client = clients.find(c => c.client_id === clientId);
      if (client) {
        // Update selected client with latest data
        setSelectedClient(client);
        console.log('🔄 Updated selected client with latest data:', client.hostname);
      } else {
        // Client not found, redirect to main page
        navigate('/clients');
      }
    } else if (clientId && clients.length === 0) {
      // Still loading clients, wait
    } else if (!clientId && selectedClient) {
      // No client in URL but one is selected, clear selection
      setSelectedClient(null);
    }
  }, [clientId, clients, navigate]);

  // Socket listeners for real-time client updates
  useEffect(() => {
    if (!socket || !isConnected) return;

    console.log('🔌 Setting up client management socket listeners');

    // Listen for client connection events
    const handleClientConnected = (data) => {
      console.log('✅ Client connected:', data);
      // Refresh client list to get updated status
      loadClients();
    };

    // Listen for client disconnection events
    const handleClientDisconnected = (data) => {
      console.log('📴 Client disconnected:', data);
      // Update client status immediately
      setClients(prevClients =>
        prevClients.map(client =>
          client.client_id === data.clientId
            ? { ...client, status: 'offline', lastSeen: new Date(data.timestamp) }
            : client
        )
      );

      // Update selected client if it's the one that disconnected
      if (selectedClient && selectedClient.client_id === data.clientId) {
        setSelectedClient(prev => ({
          ...prev,
          status: 'offline',
          last_seen: data.timestamp,
          lastSeen: new Date(data.timestamp)
        }));
      }
    };

    // Listen for client status updates
    const handleClientStatusUpdate = (data) => {
      console.log('📊 Client status update:', data);
      // Update specific client status
      setClients(prevClients =>
        prevClients.map(client =>
          client.client_id === data.clientId
            ? {
                ...client,
                status: data.status || 'online',
                lastSeen: new Date(data.timestamp),
                last_seen: data.timestamp,
                total_tasks: data.total_tasks || client.total_tasks,
                active_tasks: data.active_tasks || client.active_tasks,
                metadata: data.systemInfo || client.metadata
              }
            : client
        )
      );

      // Update selected client if it's the one that was updated
      if (selectedClient && selectedClient.client_id === data.clientId) {
        setSelectedClient(prev => ({
          ...prev,
          status: data.status || 'online',
          last_seen: data.timestamp,
          lastSeen: new Date(data.timestamp),
          total_tasks: data.total_tasks || prev.total_tasks,
          active_tasks: data.active_tasks || prev.active_tasks,
          metadata: data.systemInfo || prev.metadata
        }));
      }
    };

    // Add event listeners
    socket.on('client-connected', handleClientConnected);
    socket.on('client-disconnected', handleClientDisconnected);
    socket.on('client-status-update', handleClientStatusUpdate);

    // Cleanup listeners on unmount
    return () => {
      socket.off('client-connected', handleClientConnected);
      socket.off('client-disconnected', handleClientDisconnected);
      socket.off('client-status-update', handleClientStatusUpdate);
      console.log('🔌 Client management socket listeners cleaned up');
    };
  }, [socket, isConnected, selectedClient, loadClients]);

  const loadClients = useCallback(async () => {
    if (!token) {
      setError('Authentication required');
      setLoading(false);
      return;
    }

    try {
      setError(null);
      console.log('🔄 Loading clients from web server...');

      const response = await fetch('http://localhost:5001/api/clients', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to load clients: ${response.status}`);
      }

      const data = await response.json();
      console.log('✅ Clients loaded:', data);

      // Transform server data to match component expectations
      const transformedClients = (data.clients || []).map(client => ({
        id: client.id,
        client_id: client.client_id, // Keep original field name for API calls
        clientId: client.client_id,   // Also provide camelCase version
        hostname: client.hostname,
        platform: client.platform,
        status: client.status,
        lastSeen: new Date(client.last_seen),
        last_seen: client.last_seen, // Keep original for display
        total_tasks: client.total_tasks || 0, // Keep original field names
        active_tasks: client.active_tasks || 0,
        tasks: client.total_tasks || 0,
        activeTasks: client.active_tasks || 0,
        version: client.version,
        arch: client.arch,
        metadata: (() => {
          try {
            if (!client.metadata) return {}, [token]);
            if (typeof client.metadata === 'object') return client.metadata;
            if (typeof client.metadata === 'string') return JSON.parse(client.metadata);
            return {};
          } catch (error) {
            console.warn('Failed to parse metadata for client:', client.client_id, error);
            return {};
          }
        })()
      }));

      setClients(transformedClients);
      console.log(`📊 Displaying ${transformedClients.length} clients`);
      console.log('🔍 Client data:', transformedClients.map(c => ({
        id: c.id,
        client_id: c.client_id,
        hostname: c.hostname
      })));
    } catch (error) {
      console.error('❌ Failed to load clients:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };
  
  const sendCommand = async (clientId, command) => {
    if (!token) {
      alert('Authentication required');
      return;
    }

    try {
      console.log(`🎛️ Sending command ${command} to client ${clientId}`);

      const response = await fetch(`http://localhost:5001/api/clients/${clientId}/command`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: command,
          data: {}
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to send command: ${response.status}`);
      }

      const result = await response.json();
      console.log('✅ Command sent successfully:', result);
      alert(`Command "${command}" sent successfully to ${clientId}`);

      // Refresh clients to get updated status
      await loadClients();
    } catch (error) {
      console.error('❌ Failed to send command:', error);
      alert(`Failed to send command: ${error.message}`);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600">Manage and control all connected desktop clients</p>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600">Loading clients...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
          <p className="text-gray-600">Manage and control all connected desktop clients</p>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading clients</h3>
              <p className="mt-1 text-sm text-red-700">{error}</p>
              <button
                onClick={loadClients}
                className="mt-2 text-sm bg-red-100 text-red-800 px-3 py-1 rounded hover:bg-red-200"
              >
                Retry
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
          <div className="flex items-center space-x-4">
            <p className="text-gray-600">Manage and control all connected desktop clients</p>
            <div className="flex items-center">
              <div className={`w-2 h-2 rounded-full mr-2 ${
                isConnected ? 'bg-green-500' : 'bg-red-500'
              }`}></div>
              <span className="text-sm text-gray-500">
                {isConnected ? 'Real-time updates active' : 'Real-time updates disconnected'}
              </span>
            </div>
          </div>
        </div>
        <button
          onClick={loadClients}
          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Refresh
        </button>
      </div>

      {clients.length === 0 ? (
        <div className="text-center py-12">
          <svg className="w-12 h-12 text-gray-300 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Desktop Clients Connected</h3>
          <p className="text-gray-500 mb-4">Start a desktop application and log in to see it here.</p>
          <button
            onClick={loadClients}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Check Again
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {clients.map((client) => (
            <div key={client.id} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${
                    client.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                  }`}></div>
                  <h3 className="text-lg font-semibold text-gray-900">{client.hostname}</h3>
                </div>
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  client.status === 'online'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {client.status}
                </span>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Platform:</span>
                  <span className="text-gray-900">{client.platform}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Tasks:</span>
                  <span className="text-gray-900">{client.total_tasks || 0} total, {client.active_tasks || 0} active</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-500">Last Seen:</span>
                  <span className="text-gray-900">{new Date(client.last_seen).toLocaleTimeString()}</span>
                </div>
              </div>

              <div className="flex space-x-2">
                <button
                  onClick={() => navigate(`/clients/${client.client_id}`)}
                  className="flex-1 px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                >
                  View Details
                </button>
                <button
                  onClick={() => sendCommand(client.client_id, 'refresh')}
                  className="px-3 py-2 bg-gray-100 text-gray-700 rounded-md text-sm hover:bg-gray-200"
                  disabled={client.status !== 'online'}
                >
                  Refresh
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Client Detail View */}
      {selectedClient && (
        <div className="space-y-6">
          {/* Breadcrumb Navigation */}
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <button
              onClick={() => navigate('/clients')}
              className="hover:text-gray-700"
            >
              Client Management
            </button>
            <span>›</span>
            <span className="text-gray-900 font-medium">{selectedClient.hostname}</span>
          </div>

          {/* Client Header */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className={`w-4 h-4 rounded-full ${
                  selectedClient.status === 'online' ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">{selectedClient.hostname}</h1>
                  <p className="text-gray-500">Client ID: {selectedClient.client_id}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                  selectedClient.status === 'online'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {selectedClient.status}
                </span>
                <button
                  onClick={() => loadClients()}
                  className="px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                >
                  Refresh
                </button>
              </div>
            </div>

          </div>

          {/* Tab Navigation */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="border-b border-gray-200">
              <nav className="flex space-x-8 px-6">
                {[
                  { id: 'overview', name: 'Overview', icon: '📊' },
                  { id: 'tasks', name: 'Sync Tasks', icon: '🔄' },
                  { id: 'history', name: 'History', icon: '📜' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center px-3 py-4 text-sm font-medium border-b-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Content */}
            <div className="p-6">
              {activeTab === 'overview' && (
                <div className="space-y-6">
                  {/* System Information */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Client ID</label>
                        <p className="text-gray-900 font-mono text-sm">{selectedClient.client_id}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Hostname</label>
                        <p className="text-gray-900">{selectedClient.hostname}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Platform</label>
                        <p className="text-gray-900">{selectedClient.platform}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Architecture</label>
                        <p className="text-gray-900">{selectedClient.arch || 'N/A'}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Last Seen</label>
                        <p className="text-gray-900">{new Date(selectedClient.last_seen).toLocaleString()}</p>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Total Tasks</label>
                        <p className="text-gray-900">{selectedClient.total_tasks || 0}</p>
                      </div>
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-3 gap-6">
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                            <span className="text-blue-600">🔄</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-500">Total Tasks</p>
                          <p className="text-2xl font-semibold text-gray-900">{selectedClient.total_tasks || 0}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <span className="text-green-600">▶️</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-500">Active Tasks</p>
                          <p className="text-2xl font-semibold text-gray-900">{selectedClient.active_tasks || 0}</p>
                        </div>
                      </div>
                    </div>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                            <span className="text-purple-600">📅</span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <p className="text-sm font-medium text-gray-500">Last Sync</p>
                          <p className="text-sm font-semibold text-gray-900">
                            {selectedClient.last_sync_time
                              ? new Date(selectedClient.last_sync_time).toLocaleDateString()
                              : 'Never'
                            }
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Remote Commands */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Remote Commands</h3>
                    <div className="grid grid-cols-2 gap-4">
                      <button
                        onClick={() => sendCommand(selectedClient.client_id, 'start-all-sync')}
                        className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={selectedClient.status !== 'online'}
                      >
                        Start All Sync
                      </button>
                      <button
                        onClick={() => sendCommand(selectedClient.client_id, 'stop-all-sync')}
                        className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={selectedClient.status !== 'online'}
                      >
                        Stop All Sync
                      </button>
                      <button
                        onClick={() => sendCommand(selectedClient.client_id, 'refresh-tasks')}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={selectedClient.status !== 'online'}
                      >
                        Refresh Tasks
                      </button>
                      <button
                        onClick={() => sendCommand(selectedClient.client_id, 'get-status')}
                        className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={selectedClient.status !== 'online'}
                      >
                        Get Status
                      </button>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'tasks' && (
                <ClientTasks client={selectedClient} />
              )}

              {activeTab === 'history' && (
                <ClientHistory client={selectedClient} />
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ClientManagement;